package it.yolo.resources;

import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.PriceNotFoundException;
import it.yolo.model.*;
import it.yolo.service.WarrantyPricingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;
import java.util.stream.Collectors;

@Path("v1/pricing")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class WarrantyPricingResource {

    private static final Logger logger = LoggerFactory.getLogger(WarrantyPricingResource.class);

    @Inject
    WarrantyPricingService warrantyPricingService;

    @POST
    @Path("/quote")
    public Response getQuote(
            @QueryParam("response_type") @DefaultValue("total") String responseType,
            @Valid BaseData<WarrantyQuoteRequest> request) {

        logger.info("Received quote request with response_type: {}, request: {}", responseType, request);

        try {
            // Validate response_type parameter
            if (!isValidResponseType(responseType)) {
                logger.error("Invalid response_type: {}", responseType);
                return Response.status(Response.Status.BAD_REQUEST)
                    .entity(createErrorResponse("Invalid response_type. Must be 'total' or 'detailed'"))
                    .build();
            }

            WarrantyQuoteRequest quoteRequest = request.getData();
            
            // Get premiums from service
            List<WarrantyPremium> premiums = warrantyPricingService.getPremiums(
                quoteRequest.getWarrantyIds(),
                quoteRequest.getDurationYears(),
                quoteRequest.getBenefitAmount()
            );

            // Create response based on response_type
            BaseData<?> response = createResponse(responseType, premiums);
            
            logger.info("Successfully processed quote request, returning {} response", responseType);
            return Response.ok(response).build();

        } catch (PriceNotFoundException e) {
            logger.error("Price not found: {}", e.getMessage());
            return Response.status(Response.Status.NOT_FOUND)
                .entity(createErrorResponse(e.getMessage()))
                .build();
                
        } catch (IllegalArgumentException e) {
            logger.error("Invalid request parameters: {}", e.getMessage());
            return Response.status(Response.Status.BAD_REQUEST)
                .entity(createErrorResponse(e.getMessage()))
                .build();
                
        } catch (Exception e) {
            logger.error("Unexpected error processing quote request", e);
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
                .entity(createErrorResponse("Internal server error"))
                .build();
        }
    }

    private boolean isValidResponseType(String responseType) {
        return "total".equals(responseType) || "detailed".equals(responseType);
    }

    private BaseData<?> createResponse(String responseType, List<WarrantyPremium> premiums) {
        String premiumCurrency = warrantyPricingService.getPremiumCurrency(premiums);
        String benefitCurrency = warrantyPricingService.getBenefitCurrency(premiums);

        if ("detailed".equals(responseType)) {
            List<WarrantyPriceDetail> details = premiums.stream()
                .map(premium -> new WarrantyPriceDetail(premium.getWarrantyId(), premium.getTotalPremium()))
                .collect(Collectors.toList());
            
            DetailedQuoteResponse detailedResponse = new DetailedQuoteResponse(details, premiumCurrency, benefitCurrency);
            return new BaseData<>(detailedResponse);
        } else {
            // Default to "total"
            var totalPremium = warrantyPricingService.calculateTotalPremium(premiums);
            TotalQuoteResponse totalResponse = new TotalQuoteResponse(totalPremium, premiumCurrency, benefitCurrency);
            return new BaseData<>(totalResponse);
        }
    }

    private BaseData<ErrorResponse> createErrorResponse(String message) {
        ErrorResponse errorResponse = new ErrorResponse();
        ErrorInfo errorInfo = new ErrorInfo();
        errorInfo.setMessage(message);
        errorResponse.setError(errorInfo);
        return new BaseData<>(errorResponse);
    }
}
