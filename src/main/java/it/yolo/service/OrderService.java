package it.yolo.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.client.OrderClientV3;
import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.OrderClientCommunicationException;
import it.yolo.exception.OrderNotFoundException;
import it.yolo.model.WarrantyDetail;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.jboss.resteasy.reactive.ClientWebApplicationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import jakarta.enterprise.context.RequestScoped;
import jakarta.enterprise.inject.Instance;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@RequestScoped
public class OrderService {

    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);

    private final OrderClientV3 orderClient;
    private final Instance<JsonWebToken> jsonWebToken;
    private final ObjectMapper objectMapper;

    @Inject
    public OrderService(@RestClient OrderClientV3 orderClient, Instance<JsonWebToken> jsonWebToken, ObjectMapper objectMapper) {
        this.orderClient = orderClient;
        this.jsonWebToken = jsonWebToken;
        this.objectMapper = objectMapper;
    }

    /**
     * Recupera l'ordine completo dal servizio order
     *
     * @param orderCode Codice dell'ordine
     * @return JsonNode dell'ordine completo
     * @throws OrderNotFoundException se l'ordine non viene trovato
     */
    @WithSpan("getOrder")
    public JsonNode getOrder(String orderCode, String technicalToken) {
        logger.info("Retrieving order with code: {}", orderCode);
        try(Response response = orderClient.findByCodeUnchecked(orderCode, technicalToken)) {
            return response.readEntity(JsonNode.class);
        } catch (ClientWebApplicationException e) {
            if (e.getResponse().getStatus() == Response.Status.NOT_FOUND.getStatusCode()) {
                logger.error("Order not found with code: {}", orderCode);
                throw new OrderNotFoundException("Order not found with code: " + orderCode);
            } else {
                logger.error("Failed to retrieve order with code: {}. Response status: {}",
                        orderCode, e.getResponse().getStatus());
                throw new OrderClientCommunicationException("Failed to retrive order: " + orderCode);
            }
        }
    }

    /**
     * Recupera i dettagli delle garanzie selezionate dall'ordine specificato
     *
     * @param orderJson JsonNode dell'ordine
     * @return Lista di dettagli delle garanzie
     */
    @WithSpan("extractWarrantyDetailsFromOrder")
    public List<WarrantyDetail> extractWarrantyDetailsFromOrder(JsonNode orderJson) {
        logger.info("Extracting warranty details from order");

        try {
            // Accedi al nodo orderItem[0].instance.chosenWarranties.data.warranties
            JsonNode warranties = orderJson
                    .path("data")
                    .path("orderItem")
                    .path(0)
                    .path("instance")
                    .path("chosenWarranties")
                    .path("data")
                    .path("warranties");

            if (warranties.isMissingNode() || warranties.isEmpty()) {
                logger.warn("No chosen warranties found in order");
                return new ArrayList<>();
            }            // Estrai i dettagli delle garanzie
            List<WarrantyDetail> warrantyDetails = new ArrayList<>();
            for (JsonNode warranty : warranties) {
                // Usa anagWarranty.id come identificativo della garanzia
                JsonNode anagWarranty = warranty.path("anagWarranty");
                Integer warrantyId = anagWarranty.path("id").asInt();
                String warrantyName = anagWarranty.path("name").asText();
                String internalCode = anagWarranty.path("internal_code").asText();

                // Estrai l'importo del beneficio dal massimale/ceiling
                String benefitAmount = extractBenefitAmount(warranty);

                if (warrantyId != null && internalCode != null && benefitAmount != null && !benefitAmount.trim().isEmpty()) {
                    warrantyDetails.add(new WarrantyDetail(warrantyId, warrantyName, benefitAmount, internalCode));
                    logger.debug("Extracted warranty: id={}, name={}, benefitAmount={}, internalCode={}",
                            warrantyId, warrantyName, benefitAmount, internalCode);
                }
            }

            logger.info("Successfully extracted {} warranty details from order", warrantyDetails.size());
            return warrantyDetails;

        } catch (Exception e) {
            logger.error("Error extracting warranty details from order", e);
            throw new RuntimeException("Error extracting warranty details from order: " + e.getMessage(), e);
        }
    }

    /**
     * Recupera i dettagli di TUTTE le garanzie dall'ordine specificato (da filterWarranties)
     *
     * @param orderJson JsonNode dell'ordine
     * @return Lista di dettagli di tutte le garanzie
     */
    @WithSpan("extractAllWarrantyDetailsFromOrder")
    public List<WarrantyDetail> extractAllWarrantyDetailsFromOrder(JsonNode orderJson) {
        logger.info("Extracting ALL warranty details from order (from filterWarranties)");

        try {
            // Accedi al nodo orderItem[0].instance.filterWarranties.data.warranties
            JsonNode warranties = orderJson
                    .path("data")
                    .path("orderItem")
                    .path(0)
                    .path("instance")
                    .path("filterWarranties");

            if (warranties.isMissingNode() || warranties.isEmpty()) {
                logger.warn("No filter warranties found in order");
                return new ArrayList<>();
            }

            // Estrai i dettagli delle garanzie
            List<WarrantyDetail> warrantyDetails = new ArrayList<>();
            for (JsonNode warranty : warranties) {
                // Usa anagWarranty.id come identificativo della garanzia
                JsonNode anagWarranty = warranty.path("anagWarranty");
                Integer warrantyId = anagWarranty.path("id").asInt();
                String warrantyName = anagWarranty.path("name").asText();
                String internalCode = anagWarranty.path("internal_code").asText();

                // Estrai l'importo del beneficio dal massimale/ceiling
                String benefitAmount = extractBenefitAmount(warranty);

                if (warrantyId != null && internalCode != null && benefitAmount != null && !benefitAmount.trim().isEmpty()) {
                    warrantyDetails.add(new WarrantyDetail(warrantyId, warrantyName, benefitAmount, internalCode));
                    logger.debug("Extracted warranty from filterWarranties: id={}, name={}, benefitAmount={}, internalCode={}",
                            warrantyId, warrantyName, benefitAmount, internalCode);
                }
            }

            logger.info("Successfully extracted {} warranty details from filterWarranties", warrantyDetails.size());
            return warrantyDetails;

        } catch (Exception e) {
            logger.error("Error extracting warranty details from filterWarranties", e);
            throw new RuntimeException("Error extracting warranty details from filterWarranties: " + e.getMessage(), e);
        }
    }

    /**
     * Estrae e unisce le garanzie da filterWarranties e chosenWarranties,
     * usando gli importi corretti selezionati dall'utente quando disponibili
     *
     * @param orderJson JsonNode dell'ordine
     * @return Lista di dettagli delle garanzie con importi corretti
     */
    @WithSpan("extractMergedWarrantyDetailsFromOrder")
    public List<WarrantyDetail> extractMergedWarrantyDetailsFromOrder(JsonNode orderJson) {
        logger.info("Extracting and merging warranty details from order");

        try {
            // Estrai tutte le garanzie da filterWarranties
            List<WarrantyDetail> allWarranties = extractAllWarrantyDetailsFromOrder(orderJson);
            if (allWarranties.isEmpty()) {
                logger.warn("No warranties found in filterWarranties");
                return new ArrayList<>();
            }

            // Estrai le garanzie selezionate da chosenWarranties per ottenere gli importi corretti
            List<WarrantyDetail> chosenWarranties = extractWarrantyDetailsFromOrder(orderJson);

            // Crea una mappa per lookup veloce delle garanzie selezionate per ID
            Map<Integer, WarrantyDetail> chosenWarrantiesMap = chosenWarranties.stream()
                .collect(Collectors.toMap(WarrantyDetail::id, Function.identity()));

            // Merge: usa gli importi da chosenWarranties quando disponibili, altrimenti mantieni quelli da filterWarranties
            List<WarrantyDetail> mergedWarranties = allWarranties.stream()
                .map(warranty -> {
                    WarrantyDetail chosenWarranty = chosenWarrantiesMap.get(warranty.id());
                    if (chosenWarranty != null) {
                        // Usa l'importo selezionato dall'utente
                        return new WarrantyDetail(
                            warranty.id(),
                            warranty.name(),
                            chosenWarranty.benefitAmount(), // Importo corretto dall'utente
                            warranty.internalCode()
                        );
                    }
                    // Mantieni l'importo di default da filterWarranties
                    return warranty;
                })
                .collect(Collectors.toList());

            logger.info("Successfully merged {} warranties, {} with user-selected amounts",
                       mergedWarranties.size(), chosenWarrantiesMap.size());
            return mergedWarranties;

        } catch (Exception e) {
            logger.error("Error extracting and merging warranty details from order", e);
            throw new RuntimeException("Error extracting and merging warranty details from order: " + e.getMessage(), e);
        }
    }

    /**
     * Recupera gli ID delle garanzie selezionate dall'ordine (da chosenWarranties)
     *
     * @param orderJson JsonNode dell'ordine
     * @return Lista degli ID delle garanzie selezionate
     */
    @WithSpan("extractChosenWarrantyIdsFromOrder")
    public List<Integer> extractChosenWarrantyIdsFromOrder(JsonNode orderJson) {
        logger.info("Extracting chosen warranty IDs from order");

        try {
            // Accedi al nodo orderItem[0].instance.chosenWarranties.data.warranties
            JsonNode warranties = orderJson
                    .path("data")
                    .path("orderItem")
                    .path(0)
                    .path("instance")
                    .path("chosenWarranties")
                    .path("data")
                    .path("warranties");

            if (warranties.isMissingNode() || warranties.isEmpty()) {
                logger.warn("No chosen warranties found in order");
                return new ArrayList<>();
            }

            // Estrai gli ID delle garanzie selezionate
            List<Integer> chosenWarrantyIds = new ArrayList<>();
            for (JsonNode warranty : warranties) {
                JsonNode anagWarranty = warranty.path("anagWarranty");
                Integer warrantyId = anagWarranty.path("id").asInt();
                if (warrantyId != null) {
                    chosenWarrantyIds.add(warrantyId);
                    logger.debug("Found chosen warranty ID: {}", warrantyId);
                }
            }

            logger.info("Successfully extracted {} chosen warranty IDs", chosenWarrantyIds.size());
            return chosenWarrantyIds;

        } catch (Exception e) {
            logger.error("Error extracting chosen warranty IDs from order", e);
            throw new RuntimeException("Error extracting chosen warranty IDs from order: " + e.getMessage(), e);
        }
    }

    /**
     * Estrae la durata in anni dall'ordine
     *
     * @param orderJson JsonNode dell'ordine
     * @return Durata in anni
     */
    // Questo metodo è deprecato perché ora si utilizza extractDurationYearsFromOrder
    @Deprecated
    @WithSpan("extractDurationYearsFromOrder")
    public int extractDurationYearsFromOrder(JsonNode orderJson) {
        logger.info("Extracting duration years from order");

        // Cerca nel nodo appropriato, ad esempio orderItems[0].instance.durationYears
        JsonNode durationNode = orderJson
                .path("data")
                .path("orderItem")
                .path(0)
                .path("insured_item")
                .path("durationYears");

        if (!durationNode.isMissingNode() && durationNode.isInt()) {
            return durationNode.asInt();
        }

        // Se non è possibile trovare la durata, restituisci un valore predefinito
        logger.warn("Could not extract duration years from order, using default value of 1");
        return 1;
    }

    /**
     * Estrae la durata del pacchetto in base alla durationType
     *
     * @param orderJson JsonNode dell'ordine --> "instance" -> "packetDuration": {
     *                    "description": "dynamicDuration",
     *                    "duration": 5,
     *                    "durationType": "year",
     *                    "maxDuration": 10,
     *                    "minDuration": 1
     *                },
     * @return Durata come valore intero, senza conversione tra anni e mesi
     */
    @WithSpan("extractPackageDurationFromOrder")
    public int extractPackageDurationFromOrder(JsonNode orderJson) {
        logger.info("Extracting package duration from order");

        JsonNode packetDuration = orderJson
                .path("data")
                .path("orderItem")
                .path(0)
                .path("instance")
                .path("packetDuration");
        if (!packetDuration.isMissingNode() && packetDuration.has("duration") && packetDuration.get("duration").isInt()) {
            int duration = packetDuration.get("duration").asInt();
            String durationType = packetDuration.has("durationType") ? packetDuration.get("durationType").asText() : "year";
            if (!"year".equalsIgnoreCase(durationType)) {
                throw new IllegalArgumentException("Unsupported durationType: " + durationType + ". Only 'year' is supported.");
            }
            logger.info("Extracted duration: {} {}", duration, durationType);
            return duration;
        }

        logger.warn("Could not extract package duration from order, packetDuration node is missing or invalid");
        throw new IllegalArgumentException("Could not extract package duration from order. Ensure the order contains a valid packetDuration node.");
    }


    /**
     * Aggiorna l'ordine con i dati della quotazione calcolata
     *
     * @param orderCode Codice dell'ordine
     * @param premiums Lista di premi calcolati
     * @param totalPremium Premio totale calcolato
     * @param premiumCurrency Valuta del premio
     * @param benefitCurrency Valuta del beneficio
     * @param monthlyPremiums Lista dei premi mensili calcolati
     * @param monthlyTotalPremium Premio mensile totale calcolato
     * @return true se l'aggiornamento ha avuto successo, false altrimenti
     */
    @WithSpan("updateOrderWithQuotation")
    public Response updateOrderWithQuotation(
            String orderCode,
            List<WarrantyPremium> premiums,
            BigDecimal totalPremium,
            String premiumCurrency,
            String benefitCurrency,
            List<WarrantyPremium> monthlyPremiums,
            BigDecimal monthlyTotalPremium,
            int durationYears,
            String technicalToken
    ) {
        return updateOrderWithQuotation(orderCode, premiums, totalPremium, premiumCurrency, benefitCurrency,
                                      monthlyPremiums, monthlyTotalPremium, durationYears, technicalToken, null);
    }

    /**
     * Aggiorna l'ordine con i dati della quotazione calcolata con supporto per garanzie selezionate
     *
     * @param orderCode Codice dell'ordine
     * @param premiums Lista di premi calcolati
     * @param totalPremium Premio totale calcolato
     * @param premiumCurrency Valuta del premio
     * @param benefitCurrency Valuta del beneficio
     * @param monthlyPremiums Lista dei premi mensili calcolati
     * @param monthlyTotalPremium Premio mensile totale calcolato
     * @param selectedWarrantyIds Lista degli ID delle garanzie selezionate
     * @return Response dell'aggiornamento
     */
    @WithSpan("updateOrderWithQuotationSelected")
    public Response updateOrderWithQuotation(
            String orderCode,
            List<WarrantyPremium> premiums,
            BigDecimal totalPremium,
            String premiumCurrency,
            String benefitCurrency,
            List<WarrantyPremium> monthlyPremiums,
            BigDecimal monthlyTotalPremium,
            int durationYears,
            String technicalToken,
            List<Integer> selectedWarrantyIds
    ) {

        logger.info("Updating order with quotation data");

        try {
            // Creiamo il JsonNode per la quotazione nel formato atteso dal microservizio dell'ordine
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode quotationNode = mapper.createObjectNode();
            ObjectNode dataNode = quotationNode.putObject("data");

            // Creiamo la quotazione annuale
            ObjectNode annualQuote = createQuoteNode(
                    mapper,
                    "ANNUAL",
                    durationYears,
                    totalPremium,
                    premiumCurrency,
                    premiums,
                    selectedWarrantyIds
            );

            // Creiamo la quotazione mensile
            ObjectNode monthlyQuote = createQuoteNode(
                    mapper,
                    "MONTHLY",
                    1,
                    monthlyTotalPremium,
                    premiumCurrency,
                    monthlyPremiums,
                    selectedWarrantyIds
            );

            // Aggiungiamo le quotazioni al nodo data
            dataNode.set("annualQuote", annualQuote);
            dataNode.set("monthlyQuote", monthlyQuote);

            logger.debug("Created quotation JSON with new structure: {}", quotationNode);

            // Esegui l'update dell'ordine tramite il client
            try {
                return orderClient.updateQuotation(orderCode, quotationNode, technicalToken);
            } catch (ClientWebApplicationException e) {
                logger.error("Failed to update order with quotation data. Response status: {}",
                        e.getResponse().getStatus());
                throw new OrderClientCommunicationException("Failed to update order with quotation data");
            }
        } catch (Exception e) {
            logger.error("Error updating order with quotation data", e);
            throw e;
        }
    }

    /**
     * Crea un nodo di quotazione secondo il modello richiesto
     *
     * @param mapper ObjectMapper da utilizzare
     * @param frequency Frequenza di pagamento (ANNUAL o MONTHLY)
     * @param totalAmount Importo totale del premio
     * @param currency Valuta del premio
     * @param warrantyPremiums Lista dei premi per garanzia
     * @return ObjectNode con la struttura della quotazione
     */
    private ObjectNode createQuoteNode(
            ObjectMapper mapper,
            String frequency,
            int duration,
            BigDecimal totalAmount,
            String currency,
            List<WarrantyPremium> warrantyPremiums
    ) {
        return createQuoteNode(mapper, frequency, duration, totalAmount, currency, warrantyPremiums, null);
    }

    /**
     * Crea un nodo di quotazione secondo il modello richiesto con supporto per garanzie selezionate
     *
     * @param mapper ObjectMapper da utilizzare
     * @param frequency Frequenza di pagamento (ANNUAL o MONTHLY)
     * @param totalAmount Importo totale del premio
     * @param currency Valuta del premio
     * @param warrantyPremiums Lista dei premi per garanzia
     * @param selectedWarrantyIds Lista degli ID delle garanzie selezionate (null = tutte selezionate)
     * @return ObjectNode con la struttura della quotazione
     */
    private ObjectNode createQuoteNode(
            ObjectMapper mapper,
            String frequency,
            int duration,
            BigDecimal totalAmount,
            String currency,
            List<WarrantyPremium> warrantyPremiums,
            List<Integer> selectedWarrantyIds
    ) {
        ObjectNode quoteNode = mapper.createObjectNode();

        // Aggiungiamo i campi richiesti
        if (totalAmount != null) {
            quoteNode.put("total", totalAmount.toString());
        } else {
            quoteNode.putNull("total");
        }
        quoteNode.put("currency", currency);
        quoteNode.put("frequency", frequency.equals("ANNUAL") ? "Annual" : "Monthly");
        quoteNode.put("duration", duration);

        // Aggiungiamo eventuali totali aggiuntivi se richiesti dal modello
        if (frequency.equals("MONTHLY") && totalAmount != null) {
            // Calcolo dell'importo annuale (mensile * 12)
            BigDecimal annualAmount = totalAmount.multiply(new BigDecimal("12"));
            quoteNode.put("annualTotal", annualAmount.toString());
        } else if (frequency.equals("MONTHLY") && totalAmount == null) {
            quoteNode.putNull("annualTotal");
        }

        // Creiamo l'array delle garanzie
        ArrayNode warrantiesArray = quoteNode.putArray("warranties");

        // Per ogni premio, creiamo un nodo garanzia
        for (WarrantyPremium premium : warrantyPremiums) {
            ObjectNode warrantyNode = mapper.createObjectNode();

            // Aggiungiamo i campi richiesti per la garanzia
            warrantyNode.put("id", premium.getWarrantyId().toString());
            warrantyNode.put("total", premium.getTotalPremium().toString());

            // Determina se la garanzia è selezionata
            boolean isSelected = selectedWarrantyIds == null ||
                    selectedWarrantyIds.contains(premium.getWarrantyId());
            warrantyNode.put("selected", isSelected);

            // Aggiungiamo il massimale
            warrantyNode.put("maximal", premium.getBenefitAmount().toString());

            // Aggiungiamo l'internalCode se disponibile
            String internalCode = premium.getInternalCode();
            if (internalCode != null) {
                warrantyNode.put("internalCode", internalCode);
            }

            // Creiamo il nodo massimale
            /*ArrayNode maximalsArray = warrantyNode.putArray("maximals");
            ObjectNode maximalNode = mapper.createObjectNode();
            maximalNode.put("id", premium.getWarrantyId().toString());
            maximalNode.put("value", premium.getBenefitAmount().toString());
            maximalNode.put("preselected", true);
            maximalsArray.add(maximalNode);*/

            // Aggiungiamo la garanzia all'array
            warrantiesArray.add(warrantyNode);
        }

        return quoteNode;
    }

    /**
     * Estrae l'importo del beneficio dal nodo della garanzia
     */
    private String extractBenefitAmount(JsonNode warranty) {
        JsonNode ceilingsNode = warranty.path("ceilings");
        if (!ceilingsNode.isMissingNode()) {
            // Prima prova a estrarre dal nodo ceilings.selected
            JsonNode selectedNode = ceilingsNode.path("selected");
            if (!selectedNode.isMissingNode() && !selectedNode.isNull()) {
                // Restituisci il valore come stringa, eliminando tutti gli spazi bianchi
                return selectedNode.asText().trim().replaceAll("\\s+", "");
            }

            // Se selected non è disponibile, prova con ceilings.preselected
            JsonNode preselectedNode = ceilingsNode.path("preselected");
            if (!preselectedNode.isMissingNode() && !preselectedNode.isNull()) {
                // Restituisci il valore come stringa, eliminando tutti gli spazi bianchi
                return preselectedNode.asText().trim().replaceAll("\\s+", "");
            }
        }

        // Se non si trova né selected né preselected, lancia un'eccezione
        String warrantyName = warranty.path("name").asText();
        String errorMessage = String.format("Could not extract benefit amount for warranty: %s. Neither ceilings.selected nor ceilings.preselected are available.", warrantyName);
        logger.error(errorMessage);
        throw new IllegalArgumentException(errorMessage);
    }
}
