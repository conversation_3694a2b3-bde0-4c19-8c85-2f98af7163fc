package it.yolo.service;

import io.opentelemetry.instrumentation.annotations.WithSpan;
import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.PriceNotFoundException;
import it.yolo.model.WarrantyDetail;
import it.yolo.repository.WarrantyPremiumRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.enterprise.context.RequestScoped;
import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

@RequestScoped
public class WarrantyPricingService {

    private static final Logger logger = LoggerFactory.getLogger(WarrantyPricingService.class);

    private final WarrantyPremiumRepository warrantyPremiumRepository;

    @Inject
    public WarrantyPricingService(WarrantyPremiumRepository warrantyPremiumRepository) {
        this.warrantyPremiumRepository = warrantyPremiumRepository;
    }

    /**
     * Calcola i premi per le garanzie specificate con la durata in anni
     *
     * @param warrantyDetails Lista di dettagli delle garanzie, ciascuna con il proprio importo di beneficio
     * @param durationYears Durata in anni (convertita in mesi)
     * @return Lista dei premi calcolati
     * @throws PriceNotFoundException se i premi non vengono trovati per tutte le garanzie
     */
    @WithSpan("getPremiums")
    public List<WarrantyPremium> getPremiums(List<WarrantyDetail> warrantyDetails, int durationYears) {
        logger.info("Getting premiums for warrantyDetails: {}, durationYears: {}", warrantyDetails, durationYears);

        if (warrantyDetails == null || warrantyDetails.isEmpty()) {
            logger.error("Warranty details list cannot be null or empty");
            throw new IllegalArgumentException("Warranty details list cannot be null or empty");
        }
        if (durationYears <= 0) {
            logger.error("Duration years must be positive");
            throw new IllegalArgumentException("Duration years must be positive");
        }

        for (WarrantyDetail detail : warrantyDetails) {
            if (detail.benefitAmount() == null || detail.benefitAmount().isBlank()) {
                logger.error("Benefit amount cannot be null or empty for warranty ID: {}", detail.id());
                throw new IllegalArgumentException("Benefit amount cannot be null or empty for warranty ID: " + detail.id());
            }/*
            BigDecimal numericAmount = convertBenefitAmountToBigDecimal(detail.benefitAmount());
            if (numericAmount == null || numericAmount.compareTo(BigDecimal.ZERO) <= 0) {
                logger.error("Benefit amount must be positive for warranty ID: {}", detail.id());
                throw new IllegalArgumentException("Benefit amount must be positive for warranty ID: " + detail.id());
            }*/
        }

        int durationMonths = durationYears * 12;
        List<WarrantyDetail> missingWarrantyDetails = warrantyPremiumRepository.findMissingWarrantyDetails(
            warrantyDetails, durationMonths);
        if (!missingWarrantyDetails.isEmpty()) {
            String message = String.format(
                "Premiums not found for warranties: %s with duration: %d months",
                missingWarrantyDetails.stream()
                    .map(d -> String.format("(id: %d, benefitAmount: %s)", d.id(), d.benefitAmount()))
                    .collect(Collectors.joining(", ")),
                durationMonths);
            logger.error(message);
            throw new PriceNotFoundException(message,
                missingWarrantyDetails.stream()
                    .map(WarrantyDetail::id)
                    .collect(Collectors.toList()));
        }

        List<WarrantyPremium> premiums = warrantyPremiumRepository.findPremiumsForWarranties(
            warrantyDetails, durationMonths);
        if (premiums == null || premiums.isEmpty()) {
            logger.warn("No premiums found for the given warranties and duration");
            return List.of();
        }
        logger.info("Successfully retrieved {} premiums", premiums.size());
        return premiums;
    }

    /**
     * Calcola i premi mensili per le garanzie specificate
     *
     * @param warrantyDetails Lista di dettagli delle garanzie, ciascuna con il proprio importo di beneficio
     * @return Lista dei premi mensili calcolati
     * @throws PriceNotFoundException se i premi non vengono trovati per tutte le garanzie
     */
    @WithSpan("getMonthlyPremiums")
    public List<WarrantyPremium> getMonthlyPremiums(List<WarrantyDetail> warrantyDetails) {
        logger.info("Getting monthly premiums for warrantyDetails: {}", warrantyDetails);
        if (warrantyDetails == null || warrantyDetails.isEmpty()) {
            logger.error("Warranty details list cannot be null or empty");
            throw new IllegalArgumentException("Warranty details list cannot be null or empty");
        }
        for (WarrantyDetail detail : warrantyDetails) {
            if (detail.benefitAmount() == null || detail.benefitAmount().isBlank()) {
                logger.error("Benefit amount cannot be null or empty for warranty ID: {}", detail.id());
                throw new IllegalArgumentException("Benefit amount cannot be null or empty for warranty ID: " + detail.id());
            }
            /*BigDecimal numericAmount = convertBenefitAmountToBigDecimal(detail.benefitAmount());
            if (numericAmount == null || numericAmount.compareTo(BigDecimal.ZERO) <= 0) {
                logger.error("Benefit amount must be positive for warranty ID: {}", detail.id());
                throw new IllegalArgumentException("Benefit amount must be positive for warranty ID: " + detail.id());
            }*/
        }
        int durationMonths = 1;
        List<WarrantyDetail> missingWarrantyDetails = warrantyPremiumRepository.findMissingWarrantyDetails(
            warrantyDetails, durationMonths);
        if (!missingWarrantyDetails.isEmpty()) {
            logger.warn("Monthly premiums not found for some warranties, will calculate from annual premiums");
            List<WarrantyPremium> annualPremiums = getPremiums(warrantyDetails, 1);
            if (annualPremiums == null || annualPremiums.isEmpty()) {
                logger.warn("No annual premiums found for the given warranties");
                return List.of();
            }
            return annualPremiums.stream().map(annual -> {
                WarrantyPremium monthly = new WarrantyPremium();
                monthly.setWarrantyId(annual.getWarrantyId());
                monthly.setBenefitAmount(annual.getBenefitAmount());
                monthly.setDurationMonths(1);
                BigDecimal monthlyPremium = annual.getTotalPremium()
                    .divide(new BigDecimal("12"), 2, RoundingMode.HALF_UP);
                monthly.setTotalPremium(monthlyPremium);
                return monthly;
            }).collect(Collectors.toList());
        }
        List<WarrantyPremium> premiums = warrantyPremiumRepository.findPremiumsForWarranties(
            warrantyDetails, durationMonths);
        if (premiums == null || premiums.isEmpty()) {
            logger.warn("No monthly premiums found for the given warranties");
            return List.of();
        }
        logger.info("Successfully retrieved {} monthly premiums", premiums.size());
        return premiums;
    }

    /**
     * Calcola il premio totale da una lista di premi
     *
     * @param premiums Lista di premi
     * @return Importo totale del premio
     */
    @WithSpan("calculateTotalPremium")
    public BigDecimal calculateTotalPremium(List<WarrantyPremium> premiums) {
        logger.info("Calculating total premium for premiums: {}", premiums);
        if (premiums == null || premiums.isEmpty()) {
            return BigDecimal.ZERO;
        }
        return premiums.stream()
            .map(WarrantyPremium::getTotalPremium)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * Calcola il premio totale solo per le garanzie selezionate
     *
     * @param premiums Lista di tutti i premi
     * @param selectedWarrantyIds Lista degli ID delle garanzie selezionate
     * @return Importo totale del premio per le garanzie selezionate, null se nessuna garanzia selezionata
     */
    @WithSpan("calculateTotalPremiumForSelected")
    public BigDecimal calculateTotalPremiumForSelected(List<WarrantyPremium> premiums, List<Integer> selectedWarrantyIds) {
        logger.info("Calculating total premium for selected warranties: {} out of {} total premiums",
                   selectedWarrantyIds != null ? selectedWarrantyIds.size() : 0,
                   premiums != null ? premiums.size() : 0);

        if (premiums == null || premiums.isEmpty()) {
            return BigDecimal.ZERO;
        }

        // Se non ci sono garanzie selezionate, restituisci null invece di ZERO
        if (selectedWarrantyIds == null || selectedWarrantyIds.isEmpty()) {
            logger.info("No warranties selected, returning null total");
            return null;
        }

        // Converti gli ID in Long per il confronto e usa Set per performance O(1)
        Set<Long> selectedWarrantyIdsLong = selectedWarrantyIds.stream()
            .map(Integer::longValue)
            .collect(Collectors.toSet());

        BigDecimal total = premiums.stream()
            .filter(premium -> selectedWarrantyIdsLong.contains(premium.getWarrantyId()))
            .map(WarrantyPremium::getTotalPremium)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        logger.info("Calculated total premium for selected warranties: {}", total);
        return total;
    }

    /**
     * Ottiene la valuta del premio dal primo premio (assumendo che tutti abbiano la stessa valuta)
     *
     * @param premiums Lista di premi
     * @return Codice valuta del premio o "EUR" come default
     */
    @WithSpan("getPremiumCurrency")
    public String getPremiumCurrency(List<WarrantyPremium> premiums) {
        logger.info("Getting premium currency for premiums: {}", premiums);
        if (premiums == null || premiums.isEmpty()) {
            return "EUR";
        }
        return premiums.get(0).getPremiumCurrency();
    }

    /**
     * Ottiene la valuta del beneficio dal primo premio (assumendo che tutti abbiano la stessa valuta)
     *
     * @param premiums Lista di premi
     * @return Codice valuta del beneficio o "EUR" come default
     */
    @WithSpan("getBenefitCurrency")
    public String getBenefitCurrency(List<WarrantyPremium> premiums) {
        logger.info("Getting benefit currency for premiums: {}", premiums);
        if (premiums == null || premiums.isEmpty()) {
            return "EUR";
        }
        return premiums.get(0).getBenefitCurrency();
    }

    /**
     * Converte un importo di beneficio da String a BigDecimal se possibile
     * 
     * @param benefitAmount L'importo di beneficio come stringa
     * @return BigDecimal se convertibile, null altrimenti
     */
    private BigDecimal convertBenefitAmountToBigDecimal(String benefitAmount) {
        if (benefitAmount == null || benefitAmount.trim().isEmpty()) {
            return null;
        }
        
        try {
            return new BigDecimal(benefitAmount.trim());
        } catch (NumberFormatException e) {
            return null;
        }
    }
}
