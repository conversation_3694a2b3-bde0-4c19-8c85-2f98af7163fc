package it.yolo.service;

import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.PriceNotFoundException;
import it.yolo.model.WarrantyDetail;
import it.yolo.repository.WarrantyPremiumRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@QuarkusTest
class WarrantyPricingServiceTest {

    @Inject
    WarrantyPricingService warrantyPricingService;

    @InjectMock
    WarrantyPremiumRepository warrantyPremiumRepository;

    private List<WarrantyDetail> warrantyDetails;
    private int durationYears;
    private List<WarrantyPremium> mockPremiums;

    @BeforeEach
    void setUp() {
        // Creazione dei dettagli delle garanzie, ciascuna con il proprio importo beneficio
        WarrantyDetail detail1 = new WarrantyDetail(1001, "Warranty 1", "1000.00");
        WarrantyDetail detail2 = new WarrantyDetail(1002, "Warranty 2", "2000.00");
        warrantyDetails = Arrays.asList(detail1, detail2);

        durationYears = 1;

        // Crea i mock dei premium con le nuove proprietà
        // Inizializza anche l'oggetto AnagWarranty per evitare NullPointerException
        it.yolo.entity.AnagWarranty anag1 = new it.yolo.entity.AnagWarranty();
        anag1.setId(1001L);
        it.yolo.entity.AnagWarranty anag2 = new it.yolo.entity.AnagWarranty();
        anag2.setId(1002L);

        WarrantyPremium premium1 = new WarrantyPremium(Long.valueOf(1001), "1000.00", 12, new BigDecimal("120.00"), "EUR", "PLN");
        premium1.setWarranty(anag1);
        WarrantyPremium premium2 = new WarrantyPremium(Long.valueOf(1002), "2000.00", 12, new BigDecimal("150.00"), "EUR", "PLN");
        premium2.setWarranty(anag2);

        mockPremiums = Arrays.asList(premium1, premium2);
    }

    @Test
    void testGetPremiums_Success() {
        // Given
        when(warrantyPremiumRepository.findMissingWarrantyDetails(anyList(), anyInt()))
            .thenReturn(List.of());
        when(warrantyPremiumRepository.findPremiumsForWarranties(anyList(), anyInt()))
            .thenReturn(mockPremiums);

        // When
        List<WarrantyPremium> result = warrantyPricingService.getPremiums(warrantyDetails, durationYears);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(Long.valueOf(1001), result.get(0).getWarrantyId());
        assertEquals(Long.valueOf(1002), result.get(1).getWarrantyId());
        assertEquals("EUR", result.get(0).getBenefitCurrency());
        assertEquals("PLN", result.get(0).getPremiumCurrency());
        assertEquals("1000.00", result.get(0).getBenefitAmount());
        assertEquals("2000.00", result.get(1).getBenefitAmount());
    }

    @Test
    void testGetPremiums_MissingPremiums() {
        // Given
        List<WarrantyDetail> missingDetails = Arrays.asList(new WarrantyDetail(1001, "Warranty 1", "1000.00"));
        when(warrantyPremiumRepository.findMissingWarrantyDetails(anyList(), anyInt()))
            .thenReturn(missingDetails);

        // When & Then
        PriceNotFoundException exception = assertThrows(PriceNotFoundException.class, () -> {
            warrantyPricingService.getPremiums(warrantyDetails, durationYears);
        });

        // Verifica che l'ID della garanzia mancante sia incluso nei dettagli dell'eccezione
        List<Integer> missingIds = exception.getMissingWarrantyIds();
        assertTrue(missingIds.contains(1001));
        assertTrue(exception.getMessage().contains("1001"));
        assertTrue(exception.getMessage().contains("1000.00")); // Verifica che l'importo beneficio sia incluso nel messaggio
    }

    @Test
    void testGetPremiums_InvalidInput() {
        // Test null warranty details
        assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingService.getPremiums(null, durationYears);
        });

        // Test empty warranty details
        assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingService.getPremiums(List.of(), durationYears);
        });

        // Test negative duration
        assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingService.getPremiums(warrantyDetails, -1);
        });

        // Test null benefit amount in warranty detail
        List<WarrantyDetail> invalidDetails = List.of(new WarrantyDetail(1001, "Warranty 1", null));
        assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingService.getPremiums(invalidDetails, durationYears);
        });
    }

    @Test
    void testCalculateTotalPremium() {
        // Test with valid premiums
        BigDecimal total = warrantyPricingService.calculateTotalPremium(mockPremiums);
        assertEquals(new BigDecimal("270.00"), total);

        // Test with empty list
        BigDecimal emptyTotal = warrantyPricingService.calculateTotalPremium(List.of());
        assertEquals(BigDecimal.ZERO, emptyTotal);

        // Test with null
        BigDecimal nullTotal = warrantyPricingService.calculateTotalPremium(null);
        assertEquals(BigDecimal.ZERO, nullTotal);
    }

    @Test
    void testGetPremiumCurrency() {
        // Test with valid premiums
        String currency = warrantyPricingService.getPremiumCurrency(mockPremiums);
        assertEquals("PLN", currency);

        // Test with empty list
        String emptyCurrency = warrantyPricingService.getPremiumCurrency(List.of());
        assertEquals("EUR", emptyCurrency);

        // Test with null
        String nullCurrency = warrantyPricingService.getPremiumCurrency(null);
        assertEquals("EUR", nullCurrency);
    }

    @Test
    void testGetBenefitCurrency() {
        // Test with valid premiums
        String currency = warrantyPricingService.getBenefitCurrency(mockPremiums);
        assertEquals("EUR", currency);

        // Test with empty list
        String emptyCurrency = warrantyPricingService.getBenefitCurrency(List.of());
        assertEquals("EUR", emptyCurrency);

        // Test with null
        String nullCurrency = warrantyPricingService.getBenefitCurrency(null);
        assertEquals("EUR", nullCurrency);
    }
}
