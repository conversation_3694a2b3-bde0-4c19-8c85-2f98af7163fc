package it.yolo.service;

import io.quarkus.test.InjectMock;
import io.quarkus.test.junit.QuarkusTest;
import it.yolo.entity.WarrantyPremium;
import it.yolo.exception.PriceNotFoundException;
import it.yolo.model.WarrantyDetail;
import it.yolo.repository.WarrantyPremiumRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import jakarta.inject.Inject;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@QuarkusTest
class WarrantyPricingServiceTest {

    @Inject
    WarrantyPricingService warrantyPricingService;

    @InjectMock
    WarrantyPremiumRepository warrantyPremiumRepository;

    private List<WarrantyDetail> warrantyDetails;
    private int durationYears;
    private List<WarrantyPremium> mockPremiums;

    @BeforeEach
    void setUp() {
        // Creazione dei dettagli delle garanzie, ciascuna con il proprio importo beneficio
        WarrantyDetail detail1 = new WarrantyDetail(1001, "Warranty 1", "1000.00", "W001");
        WarrantyDetail detail2 = new WarrantyDetail(1002, "Warranty 2", "2000.00", "W002");
        warrantyDetails = Arrays.asList(detail1, detail2);

        durationYears = 1;

        // Crea i mock dei premium con le nuove proprietà
        // Inizializza anche l'oggetto AnagWarranty per evitare NullPointerException
        it.yolo.entity.AnagWarranty anag1 = new it.yolo.entity.AnagWarranty();
        anag1.setId(1001);
        anag1.setInternalCode("W001");
        it.yolo.entity.AnagWarranty anag2 = new it.yolo.entity.AnagWarranty();
        anag2.setId(1002);
        anag2.setInternalCode("W002");

        WarrantyPremium premium1 = new WarrantyPremium("1000.00", 12, new BigDecimal("120.00"), "EUR", "PLN");
        premium1.setWarranty(anag1);
        WarrantyPremium premium2 = new WarrantyPremium("2000.00", 12, new BigDecimal("150.00"), "EUR", "PLN");
        premium2.setWarranty(anag2);

        mockPremiums = Arrays.asList(premium1, premium2);
    }

    @Test
    void testGetPremiums_Success() {
        // Given
        when(warrantyPremiumRepository.findMissingWarrantyDetails(anyList(), anyInt()))
            .thenReturn(List.of());
        when(warrantyPremiumRepository.findPremiumsForWarranties(anyList(), anyInt()))
            .thenReturn(mockPremiums);

        // When
        List<WarrantyPremium> result = warrantyPricingService.getPremiums(warrantyDetails, durationYears);

        // Then
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1001, result.get(0).getWarrantyId());
        assertEquals(1002, result.get(1).getWarrantyId());
        assertEquals("EUR", result.get(0).getBenefitCurrency());
        assertEquals("PLN", result.get(0).getPremiumCurrency());
        assertEquals("1000.00", result.get(0).getBenefitAmount());
        assertEquals("2000.00", result.get(1).getBenefitAmount());
    }

    @Test
    void testGetPremiums_MissingPremiums() {
        // Given
        List<WarrantyDetail> missingDetails = Arrays.asList(new WarrantyDetail(1001, "Warranty 1", "1000.00", "W001"));
        when(warrantyPremiumRepository.findMissingWarrantyDetails(anyList(), anyInt()))
            .thenReturn(missingDetails);

        // When & Then
        PriceNotFoundException exception = assertThrows(PriceNotFoundException.class, () -> {
            warrantyPricingService.getPremiums(warrantyDetails, durationYears);
        });

        // Verifica che l'ID della garanzia mancante sia incluso nei dettagli dell'eccezione
        List<Integer> missingIds = exception.getMissingWarrantyIds();
        assertTrue(missingIds.contains(1001));
        assertTrue(exception.getMessage().contains("1001"));
        assertTrue(exception.getMessage().contains("1000.00")); // Verifica che l'importo beneficio sia incluso nel messaggio
    }

    @Test
    void testGetPremiums_InvalidInput() {
        // Test null warranty details
        assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingService.getPremiums(null, durationYears);
        });

        // Test empty warranty details
        assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingService.getPremiums(List.of(), durationYears);
        });

        // Test negative duration
        assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingService.getPremiums(warrantyDetails, -1);
        });

        // Test null benefit amount in warranty detail
        List<WarrantyDetail> invalidDetails = List.of(new WarrantyDetail(1001, "Warranty 1", null, "W001"));
        assertThrows(IllegalArgumentException.class, () -> {
            warrantyPricingService.getPremiums(invalidDetails, durationYears);
        });
    }

    @Test
    void testCalculateTotalPremium() {
        // Test with valid premiums
        BigDecimal total = warrantyPricingService.calculateTotalPremium(mockPremiums);
        assertEquals(new BigDecimal("270.00"), total);

        // Test with empty list
        BigDecimal emptyTotal = warrantyPricingService.calculateTotalPremium(List.of());
        assertEquals(BigDecimal.ZERO, emptyTotal);

        // Test with null
        BigDecimal nullTotal = warrantyPricingService.calculateTotalPremium(null);
        assertEquals(BigDecimal.ZERO, nullTotal);
    }

    @Test
    void testCalculateTotalPremiumForSelected() {
        // Test with selected warranties (only first warranty)
        List<Integer> selectedIds = Arrays.asList(1001);
        BigDecimal selectedTotal = warrantyPricingService.calculateTotalPremiumForSelected(mockPremiums, selectedIds);
        assertEquals(new BigDecimal("120.00"), selectedTotal);

        // Test with all warranties selected
        List<Integer> allSelectedIds = Arrays.asList(1001, 1002);
        BigDecimal allTotal = warrantyPricingService.calculateTotalPremiumForSelected(mockPremiums, allSelectedIds);
        assertEquals(new BigDecimal("270.00"), allTotal);

        // Test with no warranties selected - should return null now
        List<Integer> noSelectedIds = Arrays.asList();
        BigDecimal noTotal = warrantyPricingService.calculateTotalPremiumForSelected(mockPremiums, noSelectedIds);
        assertNull(noTotal, "Should return null when no warranties are selected");

        // Test with non-existing warranty ID
        List<Integer> nonExistingIds = Arrays.asList(9999);
        BigDecimal nonExistingTotal = warrantyPricingService.calculateTotalPremiumForSelected(mockPremiums, nonExistingIds);
        assertEquals(BigDecimal.ZERO, nonExistingTotal);

        // Test with null selected IDs - should return null now
        BigDecimal nullSelectedTotal = warrantyPricingService.calculateTotalPremiumForSelected(mockPremiums, null);
        assertNull(nullSelectedTotal, "Should return null when selected IDs are null");

        // Test with null premiums
        BigDecimal nullPremiumsTotal = warrantyPricingService.calculateTotalPremiumForSelected(null, selectedIds);
        assertEquals(BigDecimal.ZERO, nullPremiumsTotal);
    }

    @Test
    void testGetPremiumCurrency() {
        // Test with valid premiums
        String currency = warrantyPricingService.getPremiumCurrency(mockPremiums);
        assertEquals("PLN", currency);

        // Test with empty list
        String emptyCurrency = warrantyPricingService.getPremiumCurrency(List.of());
        assertEquals("EUR", emptyCurrency);

        // Test with null
        String nullCurrency = warrantyPricingService.getPremiumCurrency(null);
        assertEquals("EUR", nullCurrency);
    }

    @Test
    void testGetBenefitCurrency() {
        // Test with valid premiums
        String currency = warrantyPricingService.getBenefitCurrency(mockPremiums);
        assertEquals("EUR", currency);

        // Test with empty list
        String emptyCurrency = warrantyPricingService.getBenefitCurrency(List.of());
        assertEquals("EUR", emptyCurrency);

        // Test with null
        String nullCurrency = warrantyPricingService.getBenefitCurrency(null);
        assertEquals("EUR", nullCurrency);
    }
}
