quarkus.datasource.db-kind=h2
quarkus.datasource.jdbc.url=jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL;INIT=CREATE SCHEMA IF NOT EXISTS PRICING\\; CREATE SCHEMA IF NOT EXISTS PRODUCT
quarkus.datasource.username=sa
quarkus.datasource.password=sa
quarkus.hibernate-orm.database.generation=drop-and-create
quarkus.datasource.jdbc.driver=org.h2.Driver
quarkus.hibernate-orm.sql-load-script=import.sql
